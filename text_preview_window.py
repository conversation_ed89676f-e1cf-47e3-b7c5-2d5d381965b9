#!/usr/bin/env python3
"""
Text Preview Window for PDF to Audiobook Converter
Author: inkbytefo
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import json
from pathlib import Path
from typing import Dict, List, Callable
import re

class TextPreviewWindow:
    """Window for previewing and editing text before TTS conversion"""
    
    def __init__(self, parent, text_data: Dict, config: Dict, callback: Callable = None):
        """
        Initialize text preview window
        
        Args:
            parent: Parent window
            text_data: Dictionary containing extracted text and chapters
            config: Application configuration
            callback: Callback function to call when user confirms
        """
        self.parent = parent
        self.text_data = text_data
        self.config = config
        self.callback = callback
        
        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("Text Preview - PDF to Audiobook")
        self.window.geometry("1200x800")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.current_chapter = tk.IntVar(value=0)
        self.show_all_text = tk.BooleanVar(value=False)
        self.selected_chapters = {}  # Track which chapters are selected
        
        # Initialize selected chapters (all selected by default)
        for i, chapter in enumerate(self.text_data.get('chapters', [])):
            self.selected_chapters[i] = tk.BooleanVar(value=True)
        
        self.setup_ui()
        self.load_text_content()
        self.calculate_statistics()
        
        # Center window
        self.center_window()

        # Find/Replace dialog reference
        self.find_dialog = None
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Text Preview & Editor", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Top control frame
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Chapter selection frame
        chapter_frame = ttk.LabelFrame(control_frame, text="Chapter Selection", padding="5")
        chapter_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # Chapter navigation
        nav_frame = ttk.Frame(chapter_frame)
        nav_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(nav_frame, text="Chapter:").pack(side=tk.LEFT)
        
        self.chapter_combo = ttk.Combobox(nav_frame, textvariable=self.current_chapter,
                                         state="readonly", width=30)
        self.chapter_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.chapter_combo.bind('<<ComboboxSelected>>', self.on_chapter_change)
        
        # Show all text option
        ttk.Checkbutton(nav_frame, text="Show All Text", 
                       variable=self.show_all_text,
                       command=self.toggle_view_mode).pack(side=tk.LEFT, padx=(10, 0))
        
        # Chapter selection checkboxes frame
        self.chapter_selection_frame = ttk.Frame(chapter_frame)
        self.chapter_selection_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Statistics frame
        stats_frame = ttk.LabelFrame(control_frame, text="Statistics", padding="5")
        stats_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.stats_label = ttk.Label(stats_frame, text="Calculating...", font=('Arial', 9))
        self.stats_label.pack()
        
        # Text editor frame
        editor_frame = ttk.LabelFrame(main_frame, text="Text Content", padding="5")
        editor_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Text editor with scrollbar
        self.text_editor = scrolledtext.ScrolledText(
            editor_frame,
            wrap=tk.WORD,
            font=('Consolas', 11),
            undo=True,
            maxundo=50
        )
        self.text_editor.pack(fill=tk.BOTH, expand=True)

        # Add text editor enhancements
        self.setup_text_editor_enhancements()

        # Bind events for real-time statistics update
        self.text_editor.bind('<KeyRelease>', self.on_text_change)
        self.text_editor.bind('<Button-1>', self.on_text_change)
        
        # Bottom button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Left side buttons
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="Save Text", 
                  command=self.save_text).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Load Text", 
                  command=self.load_text).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Reset",
                  command=self.reset_text).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Find & Replace",
                  command=self.show_find_replace).pack(side=tk.LEFT, padx=(0, 5))
        
        # Right side buttons
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="Cancel", 
                  command=self.cancel).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(right_buttons, text="Convert to Audio", 
                  command=self.confirm_conversion,
                  style='Accent.TButton').pack(side=tk.LEFT)
        
        # Setup chapter combo values
        self.update_chapter_combo()
        self.setup_chapter_checkboxes()
    
    def update_chapter_combo(self):
        """Update chapter combo box values"""
        chapters = self.text_data.get('chapters', [])
        if chapters:
            chapter_names = [f"{i+1}. {chapter.get('title', f'Chapter {i+1}')[:50]}" 
                           for i, chapter in enumerate(chapters)]
            self.chapter_combo['values'] = chapter_names
            if chapter_names:
                self.chapter_combo.current(0)
        else:
            self.chapter_combo['values'] = ["1. Full Document"]
            self.chapter_combo.current(0)
    
    def setup_chapter_checkboxes(self):
        """Setup chapter selection checkboxes"""
        # Clear existing checkboxes
        for widget in self.chapter_selection_frame.winfo_children():
            widget.destroy()
        
        chapters = self.text_data.get('chapters', [])
        if len(chapters) > 1:
            ttk.Label(self.chapter_selection_frame, 
                     text="Select chapters to convert:").pack(anchor=tk.W)
            
            # Create scrollable frame for checkboxes
            checkbox_canvas = tk.Canvas(self.chapter_selection_frame, height=60)
            checkbox_scrollbar = ttk.Scrollbar(self.chapter_selection_frame, 
                                             orient="vertical", 
                                             command=checkbox_canvas.yview)
            checkbox_frame = ttk.Frame(checkbox_canvas)
            
            checkbox_frame.bind(
                "<Configure>",
                lambda e: checkbox_canvas.configure(scrollregion=checkbox_canvas.bbox("all"))
            )
            
            checkbox_canvas.create_window((0, 0), window=checkbox_frame, anchor="nw")
            checkbox_canvas.configure(yscrollcommand=checkbox_scrollbar.set)
            
            # Add checkboxes
            for i, chapter in enumerate(chapters):
                cb = ttk.Checkbutton(
                    checkbox_frame,
                    text=f"{i+1}. {chapter.get('title', f'Chapter {i+1}')[:40]}",
                    variable=self.selected_chapters[i],
                    command=self.calculate_statistics
                )
                cb.pack(anchor=tk.W, padx=(10, 0))
            
            checkbox_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            checkbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        else:
            ttk.Label(self.chapter_selection_frame, 
                     text="Single document - all content will be converted").pack()
    
    def on_chapter_change(self, event=None):
        """Handle chapter selection change"""
        if not self.show_all_text.get():
            self.load_text_content()
    
    def toggle_view_mode(self):
        """Toggle between chapter view and full text view"""
        self.load_text_content()
    
    def load_text_content(self):
        """Load text content into editor"""
        self.text_editor.delete(1.0, tk.END)
        
        if self.show_all_text.get():
            # Show all text
            full_text = self.text_data.get('text', '')
            self.text_editor.insert(1.0, full_text)
        else:
            # Show selected chapter
            chapters = self.text_data.get('chapters', [])
            if chapters:
                chapter_index = self.current_chapter.get()
                if 0 <= chapter_index < len(chapters):
                    chapter = chapters[chapter_index]
                    content = f"# {chapter.get('title', f'Chapter {chapter_index + 1}')}\n\n"
                    content += chapter.get('content', '')
                    self.text_editor.insert(1.0, content)
            else:
                # No chapters, show full text
                full_text = self.text_data.get('text', '')
                self.text_editor.insert(1.0, full_text)
    
    def calculate_statistics(self):
        """Calculate and display text statistics"""
        try:
            # Get selected text
            if self.show_all_text.get():
                text = self.text_editor.get(1.0, tk.END)
            else:
                # Calculate for selected chapters only
                chapters = self.text_data.get('chapters', [])
                total_text = ""

                for i, chapter in enumerate(chapters):
                    if i in self.selected_chapters and self.selected_chapters[i].get():
                        total_text += chapter.get('content', '') + "\n\n"

                if not total_text:
                    total_text = self.text_data.get('text', '')

                text = total_text

            # Calculate statistics
            char_count = len(text.strip())
            word_count = len(text.split())

            # Estimate duration (average TTS speed: 180 words per minute)
            estimated_minutes = word_count / 180
            hours = int(estimated_minutes // 60)
            minutes = int(estimated_minutes % 60)

            # Calculate ElevenLabs cost based on current pricing (2024)
            estimated_cost = self.calculate_elevenlabs_cost(char_count)

            # Update statistics display
            stats_text = f"Characters: {char_count:,}\n"
            stats_text += f"Words: {word_count:,}\n"
            stats_text += f"Est. Duration: {hours}h {minutes}m\n"
            stats_text += f"Est. Cost: ${estimated_cost:.2f}\n"
            stats_text += f"Credits needed: {char_count:,}"

            self.stats_label.config(text=stats_text)

        except Exception as e:
            self.stats_label.config(text=f"Error calculating stats: {e}")

    def calculate_elevenlabs_cost(self, char_count):
        """Calculate ElevenLabs cost based on character count and pricing tiers"""
        # ElevenLabs pricing tiers (as of 2024)
        pricing_tiers = [
            {"name": "Free", "included": 20000, "overage": 0, "monthly": 0},
            {"name": "Starter", "included": 60000, "overage": 0.15, "monthly": 5},
            {"name": "Creator", "included": 200000, "overage": 0.15, "monthly": 22},
            {"name": "Pro", "included": 1000000, "overage": 0.12, "monthly": 99},
            {"name": "Scale", "included": 4000000, "overage": 0.09, "monthly": 330},
            {"name": "Business", "included": 22000000, "overage": 0.06, "monthly": 1320}
        ]

        # Calculate cost for each tier
        costs = []

        for tier in pricing_tiers:
            if char_count <= tier["included"]:
                # Within included limit
                cost = tier["monthly"]
            else:
                # Exceeds included limit
                overage_chars = char_count - tier["included"]
                overage_cost = (overage_chars / 1000) * tier["overage"]
                cost = tier["monthly"] + overage_cost

            costs.append({
                "tier": tier["name"],
                "cost": cost,
                "monthly": tier["monthly"]
            })

        # Find the most cost-effective tier
        best_tier = min(costs, key=lambda x: x["cost"])

        # Return the cost of the best tier
        return best_tier["cost"]
    
    def save_text(self):
        """Save current text to file with metadata"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Text Project",
                defaultextension=".json",
                filetypes=[
                    ("PDF Audiobook Project", "*.json"),
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                if filename.endswith('.json'):
                    # Save as project file with metadata
                    project_data = {
                        "text_content": self.text_editor.get(1.0, tk.END),
                        "original_data": self.text_data,
                        "selected_chapters": {str(k): v.get() for k, v in self.selected_chapters.items()},
                        "view_mode": "all_text" if self.show_all_text.get() else "chapter",
                        "current_chapter": self.current_chapter.get(),
                        "metadata": {
                            "saved_at": str(Path().absolute()),
                            "version": "1.0"
                        }
                    }

                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(project_data, f, indent=2, ensure_ascii=False)

                    messagebox.showinfo("Success", f"Project saved to {filename}")
                else:
                    # Save as plain text
                    text_content = self.text_editor.get(1.0, tk.END)
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(text_content)
                    messagebox.showinfo("Success", f"Text saved to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save: {e}")

    def load_text(self):
        """Load text from file or project"""
        try:
            filename = filedialog.askopenfilename(
                title="Load Text or Project",
                filetypes=[
                    ("PDF Audiobook Project", "*.json"),
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                if filename.endswith('.json'):
                    # Load project file
                    with open(filename, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)

                    # Restore text content
                    self.text_editor.delete(1.0, tk.END)
                    self.text_editor.insert(1.0, project_data.get("text_content", ""))

                    # Restore original data if available
                    if "original_data" in project_data:
                        self.text_data = project_data["original_data"]
                        self.update_chapter_combo()
                        self.setup_chapter_checkboxes()

                    # Restore selected chapters
                    if "selected_chapters" in project_data:
                        for k, v in project_data["selected_chapters"].items():
                            if int(k) in self.selected_chapters:
                                self.selected_chapters[int(k)].set(v)

                    # Restore view mode
                    if project_data.get("view_mode") == "all_text":
                        self.show_all_text.set(True)
                    else:
                        self.show_all_text.set(False)
                        if "current_chapter" in project_data:
                            self.current_chapter.set(project_data["current_chapter"])

                    self.calculate_statistics()
                    messagebox.showinfo("Success", f"Project loaded from {filename}")

                else:
                    # Load plain text
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()

                    self.text_editor.delete(1.0, tk.END)
                    self.text_editor.insert(1.0, content)
                    self.calculate_statistics()
                    messagebox.showinfo("Success", f"Text loaded from {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load: {e}")
    
    def reset_text(self):
        """Reset text to original"""
        if messagebox.askyesno("Confirm Reset", 
                              "Are you sure you want to reset the text to original?"):
            self.load_text_content()
            self.calculate_statistics()
    
    def get_final_text_data(self) -> Dict:
        """Get the final text data for conversion"""
        if self.show_all_text.get():
            # Return modified full text
            modified_text = self.text_editor.get(1.0, tk.END).strip()
            return {
                'text': modified_text,
                'chapters': [{'title': 'Full Document', 'content': modified_text}],
                'metadata': self.text_data.get('metadata', {})
            }
        else:
            # Return selected chapters with modifications
            chapters = self.text_data.get('chapters', [])
            selected_chapters = []
            
            for i, chapter in enumerate(chapters):
                if i in self.selected_chapters and self.selected_chapters[i].get():
                    if i == self.current_chapter.get():
                        # This chapter might be modified
                        content = self.text_editor.get(1.0, tk.END).strip()
                        # Remove the title line if it exists
                        lines = content.split('\n')
                        if lines and lines[0].startswith('#'):
                            content = '\n'.join(lines[2:])  # Skip title and empty line
                        
                        selected_chapters.append({
                            'title': chapter.get('title', f'Chapter {i+1}'),
                            'content': content
                        })
                    else:
                        selected_chapters.append(chapter)
            
            # Combine all selected chapters
            combined_text = '\n\n'.join([ch['content'] for ch in selected_chapters])
            
            return {
                'text': combined_text,
                'chapters': selected_chapters,
                'metadata': self.text_data.get('metadata', {})
            }
    
    def confirm_conversion(self):
        """Confirm and start conversion"""
        try:
            # Get final text data
            final_data = self.get_final_text_data()
            
            # Check if any text is selected
            if not final_data['text'].strip():
                messagebox.showwarning("Warning", "No text selected for conversion!")
                return
            
            # Confirm with user
            stats = self.stats_label.cget('text')
            message = f"Ready to convert to audiobook:\n\n{stats}\n\nProceed with conversion?"
            
            if messagebox.askyesno("Confirm Conversion", message):
                # Close window and call callback
                self.window.destroy()
                if self.callback:
                    self.callback(final_data)
                    
        except Exception as e:
            messagebox.showerror("Error", f"Error preparing conversion: {e}")
    
    def cancel(self):
        """Cancel and close window"""
        if messagebox.askyesno("Cancel", "Are you sure you want to cancel?"):
            self.window.destroy()
    
    def setup_text_editor_enhancements(self):
        """Setup text editor enhancements"""
        # Add context menu
        self.context_menu = tk.Menu(self.text_editor, tearoff=0)
        self.context_menu.add_command(label="Cut", command=lambda: self.text_editor.event_generate("<<Cut>>"))
        self.context_menu.add_command(label="Copy", command=lambda: self.text_editor.event_generate("<<Copy>>"))
        self.context_menu.add_command(label="Paste", command=lambda: self.text_editor.event_generate("<<Paste>>"))
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Select All", command=lambda: self.text_editor.tag_add("sel", "1.0", "end"))
        self.context_menu.add_command(label="Find & Replace", command=self.show_find_replace)

        # Bind right-click
        self.text_editor.bind("<Button-3>", self.show_context_menu)

        # Add keyboard shortcuts
        self.text_editor.bind("<Control-f>", lambda e: self.show_find_replace())
        self.text_editor.bind("<Control-h>", lambda e: self.show_find_replace())

    def show_context_menu(self, event):
        """Show context menu"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def on_text_change(self, event=None):
        """Handle text change events"""
        # Update statistics with a small delay to avoid too frequent updates
        self.window.after(500, self.calculate_statistics)

    def show_find_replace(self):
        """Show find and replace dialog"""
        if self.find_dialog and self.find_dialog.winfo_exists():
            self.find_dialog.lift()
            return

        self.find_dialog = tk.Toplevel(self.window)
        self.find_dialog.title("Find & Replace")
        self.find_dialog.geometry("400x200")
        self.find_dialog.transient(self.window)

        # Find frame
        find_frame = ttk.Frame(self.find_dialog, padding="10")
        find_frame.pack(fill=tk.X)

        ttk.Label(find_frame, text="Find:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.find_entry = ttk.Entry(find_frame, width=40)
        self.find_entry.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=2)

        ttk.Label(find_frame, text="Replace:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.replace_entry = ttk.Entry(find_frame, width=40)
        self.replace_entry.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=2)

        find_frame.columnconfigure(1, weight=1)

        # Options frame
        options_frame = ttk.Frame(self.find_dialog, padding="10")
        options_frame.pack(fill=tk.X)

        self.case_sensitive = tk.BooleanVar()
        self.whole_word = tk.BooleanVar()

        ttk.Checkbutton(options_frame, text="Case sensitive",
                       variable=self.case_sensitive).pack(side=tk.LEFT)
        ttk.Checkbutton(options_frame, text="Whole word",
                       variable=self.whole_word).pack(side=tk.LEFT, padx=(10, 0))

        # Buttons frame
        buttons_frame = ttk.Frame(self.find_dialog, padding="10")
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="Find Next",
                  command=self.find_next).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Replace",
                  command=self.replace_current).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Replace All",
                  command=self.replace_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Close",
                  command=self.find_dialog.destroy).pack(side=tk.RIGHT)

        # Focus on find entry
        self.find_entry.focus()

        # Bind Enter key
        self.find_entry.bind('<Return>', lambda e: self.find_next())
        self.replace_entry.bind('<Return>', lambda e: self.replace_current())

    def find_next(self):
        """Find next occurrence"""
        search_text = self.find_entry.get()
        if not search_text:
            return

        # Get current cursor position
        current_pos = self.text_editor.index(tk.INSERT)

        # Search from current position
        if self.case_sensitive.get():
            pos = self.text_editor.search(search_text, current_pos, tk.END)
        else:
            pos = self.text_editor.search(search_text, current_pos, tk.END, nocase=True)

        if pos:
            # Select found text
            end_pos = f"{pos}+{len(search_text)}c"
            self.text_editor.tag_remove("sel", "1.0", tk.END)
            self.text_editor.tag_add("sel", pos, end_pos)
            self.text_editor.mark_set(tk.INSERT, end_pos)
            self.text_editor.see(pos)
        else:
            # Search from beginning
            if self.case_sensitive.get():
                pos = self.text_editor.search(search_text, "1.0", current_pos)
            else:
                pos = self.text_editor.search(search_text, "1.0", current_pos, nocase=True)

            if pos:
                end_pos = f"{pos}+{len(search_text)}c"
                self.text_editor.tag_remove("sel", "1.0", tk.END)
                self.text_editor.tag_add("sel", pos, end_pos)
                self.text_editor.mark_set(tk.INSERT, end_pos)
                self.text_editor.see(pos)
            else:
                messagebox.showinfo("Find", "Text not found")

    def replace_current(self):
        """Replace current selection"""
        try:
            if self.text_editor.tag_ranges("sel"):
                self.text_editor.delete("sel.first", "sel.last")
                self.text_editor.insert(tk.INSERT, self.replace_entry.get())
                self.find_next()
        except tk.TclError:
            self.find_next()

    def replace_all(self):
        """Replace all occurrences"""
        search_text = self.find_entry.get()
        replace_text = self.replace_entry.get()

        if not search_text:
            return

        content = self.text_editor.get("1.0", tk.END)

        if self.case_sensitive.get():
            new_content = content.replace(search_text, replace_text)
        else:
            # Case insensitive replace
            import re
            pattern = re.compile(re.escape(search_text), re.IGNORECASE)
            new_content = pattern.sub(replace_text, content)

        if new_content != content:
            self.text_editor.delete("1.0", tk.END)
            self.text_editor.insert("1.0", new_content)
            count = content.count(search_text) if self.case_sensitive.get() else len(re.findall(re.escape(search_text), content, re.IGNORECASE))
            messagebox.showinfo("Replace All", f"Replaced {count} occurrences")
        else:
            messagebox.showinfo("Replace All", "No occurrences found")

    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')


def main():
    """Test the text preview window"""
    # Sample data for testing
    sample_data = {
        'text': 'This is a sample text for testing the preview window.',
        'chapters': [
            {'title': 'Chapter 1: Introduction', 'content': 'This is the first chapter content.'},
            {'title': 'Chapter 2: Main Content', 'content': 'This is the second chapter content.'}
        ],
        'metadata': {'title': 'Sample Book', 'author': 'Test Author', 'pages': 10}
    }
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    def test_callback(data):
        print("Conversion confirmed with data:", data)
        root.quit()
    
    preview = TextPreviewWindow(root, sample_data, {}, test_callback)
    root.mainloop()


if __name__ == "__main__":
    main()
