#!/usr/bin/env python3
"""
PDF to Audiobook Converter - GUI Interface
Author: inkbytefo
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import logging
from pathlib import Path
import json
from pdf_to_audiobook import PDFToAudiobookConverter
from text_preview_window import TextPreviewWindow

class AudiobookConverterGUI:
    """Simple GUI interface for PDF to Audiobook conversion"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("PDF to Audiobook Converter")
        self.root.geometry("800x600")
        
        # Initialize converter
        self.converter = PDFToAudiobookConverter()
        
        # Queue for thread communication
        self.queue = queue.Queue()
        
        # Variables
        self.pdf_path = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.voice_id = tk.StringVar()
        self.extraction_method = tk.StringVar(value="auto")
        self.enable_enhancement = tk.BooleanVar(value=False)
        self.enhancement_type = tk.StringVar(value="comprehensive")
        
        # Conversion state
        self.is_converting = False
        self.is_processing = False
        self.extracted_text_data = None

        self.setup_ui()
        self.load_voices()

        # Start queue processing
        self.process_queue()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="PDF to Audiobook Converter", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # PDF File Selection
        ttk.Label(main_frame, text="PDF File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.pdf_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", command=self.browse_pdf).grid(row=1, column=2, pady=5)
        
        # Output Directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_dir, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", command=self.browse_output).grid(row=2, column=2, pady=5)
        
        # Voice Selection
        ttk.Label(main_frame, text="Voice:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.voice_combo = ttk.Combobox(main_frame, textvariable=self.voice_id, width=47)
        self.voice_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Refresh", command=self.load_voices).grid(row=3, column=2, pady=5)
        
        # Extraction Method
        ttk.Label(main_frame, text="Extraction Method:").grid(row=4, column=0, sticky=tk.W, pady=5)
        method_combo = ttk.Combobox(main_frame, textvariable=self.extraction_method, 
                                   values=["auto", "pdfplumber", "pypdf2", "pymupdf"],
                                   state="readonly", width=47)
        method_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        
        # Options Frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
        options_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(0, weight=1)
        
        # Checkboxes for options
        self.create_combined = tk.BooleanVar(value=True)
        self.create_chapters = tk.BooleanVar(value=True)
        self.optimize_streaming = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(options_frame, text="Create combined audiobook file",
                       variable=self.create_combined).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Create separate chapter files",
                       variable=self.create_chapters).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Optimize for streaming",
                       variable=self.optimize_streaming).grid(row=2, column=0, sticky=tk.W)

        # Text Enhancement Options
        enhancement_frame = ttk.Frame(options_frame)
        enhancement_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=5)
        enhancement_frame.columnconfigure(1, weight=1)

        ttk.Checkbutton(enhancement_frame, text="Enable AI text enhancement",
                       variable=self.enable_enhancement).grid(row=0, column=0, sticky=tk.W)

        ttk.Label(enhancement_frame, text="Type:").grid(row=0, column=1, sticky=tk.W, padx=(20, 5))
        enhancement_combo = ttk.Combobox(enhancement_frame, textvariable=self.enhancement_type,
                                       values=["comprehensive", "spelling", "grammar", "tts_only"],
                                       state="readonly", width=15)
        enhancement_combo.grid(row=0, column=2, sticky=tk.W)

        # Test Enhancement Button
        ttk.Button(enhancement_frame, text="Test AI Connection",
                  command=self.test_enhancement).grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        
        # Convert Button
        self.convert_button = ttk.Button(main_frame, text="Process PDF & Preview Text",
                                        command=self.start_processing)
        self.convert_button.grid(row=6, column=0, columnspan=3, pady=20)
        
        # Progress Bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # Status Label
        self.status_label = ttk.Label(main_frame, text="Ready to convert")
        self.status_label.grid(row=8, column=0, columnspan=3, pady=5)
        
        # Log Text Area
        log_frame = ttk.LabelFrame(main_frame, text="Conversion Log", padding="5")
        log_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(9, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Setup logging to GUI
        self.setup_gui_logging()
    
    def setup_gui_logging(self):
        """Setup logging to display in GUI"""
        class GUILogHandler(logging.Handler):
            def __init__(self, queue):
                super().__init__()
                self.queue = queue
            
            def emit(self, record):
                self.queue.put(('log', self.format(record)))
        
        # Add GUI handler to root logger
        gui_handler = GUILogHandler(self.queue)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(gui_handler)
        logging.getLogger().setLevel(logging.INFO)
    
    def browse_pdf(self):
        """Browse for PDF file"""
        filename = filedialog.askopenfilename(
            title="Select PDF file",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.pdf_path.set(filename)
            
            # Auto-set output directory
            if not self.output_dir.get():
                pdf_path = Path(filename)
                output_path = pdf_path.parent / "audiobooks" / pdf_path.stem
                self.output_dir.set(str(output_path))
    
    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select output directory")
        if directory:
            self.output_dir.set(directory)
    
    def load_voices(self):
        """Load available voices"""
        try:
            self.status_label.config(text="Loading voices...")
            voices = self.converter.list_available_voices()
            
            voice_options = []
            for voice in voices:
                voice_options.append(f"{voice['voice_id']} - {voice['name']} ({voice['category']})")
            
            self.voice_combo['values'] = voice_options
            if voice_options:
                self.voice_combo.current(0)
            
            self.status_label.config(text=f"Loaded {len(voices)} voices")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load voices: {e}")
            self.status_label.config(text="Error loading voices")

    def test_enhancement(self):
        """Test AI text enhancement connection"""
        try:
            self.status_label.config(text="Testing AI connection...")
            self.converter.initialize_text_enhancer()

            if self.converter.text_enhancer:
                test_result = self.converter.text_enhancer.test_connection()
                if test_result['success']:
                    messagebox.showinfo("Success",
                                      f"AI connection successful!\n\n"
                                      f"Model: {test_result['model']}\n"
                                      f"Base URL: {test_result['base_url']}")
                    self.status_label.config(text="AI connection successful")
                else:
                    messagebox.showerror("Error",
                                       f"AI connection failed:\n{test_result['message']}")
                    self.status_label.config(text="AI connection failed")
            else:
                messagebox.showerror("Error",
                                   "Text enhancement not enabled or API key not found.\n\n"
                                   "Please check your .env file for:\n"
                                   "- OPENAI_API_KEY\n"
                                   "- OPENAI_BASE_URL\n"
                                   "- OPENAI_MODEL")
                self.status_label.config(text="AI not configured")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to test AI connection: {e}")
            self.status_label.config(text="AI test failed")

    def start_processing(self):
        """Start the PDF processing and text extraction"""
        if self.is_processing or self.is_converting:
            return

        # Validate inputs
        if not self.pdf_path.get():
            messagebox.showerror("Error", "Please select a PDF file")
            return

        # Start processing in thread
        self.is_processing = True
        self.convert_button.config(state='disabled', text='Processing PDF...')
        self.progress.start()
        self.status_label.config(text="Extracting and processing text...")

        processing_thread = threading.Thread(
            target=self.run_text_processing,
            args=(self.pdf_path.get(), self.extraction_method.get())
        )
        processing_thread.daemon = True
        processing_thread.start()

    def run_text_processing(self, pdf_path, extraction_method):
        """Run text extraction and enhancement"""
        try:
            # Step 1: Extract text from PDF
            self.queue.put(('log', "Extracting text from PDF..."))
            extraction_result = self.converter.pdf_extractor.extract_text(
                pdf_path=pdf_path,
                method=extraction_method
            )

            # Step 2: Clean text
            self.queue.put(('log', "Cleaning text..."))
            if self.converter.config['text']['clean_text']:
                cleaned_text = self.converter.text_cleaner.clean_text(extraction_result['text'])
                extraction_result['text'] = cleaned_text

                # Clean chapter content too
                for chapter in extraction_result['chapters']:
                    chapter['content'] = self.converter.text_cleaner.clean_text(chapter['content'])

            # Step 3: Enhance text if enabled
            if self.enable_enhancement.get():
                self.queue.put(('log', "Enhancing text with AI..."))
                self.converter.initialize_text_enhancer()

                if self.converter.text_enhancer:
                    # Enhance main text
                    enhancement_result = self.converter.text_enhancer.enhance_text(
                        extraction_result['text'],
                        self.enhancement_type.get()
                    )
                    extraction_result['text'] = enhancement_result['enhanced']

                    # Enhance chapters
                    for chapter in extraction_result['chapters']:
                        chapter_enhancement = self.converter.text_enhancer.enhance_text(
                            chapter['content'],
                            self.enhancement_type.get()
                        )
                        chapter['content'] = chapter_enhancement['enhanced']

            self.queue.put(('text_ready', extraction_result))

        except Exception as e:
            self.queue.put(('error', str(e)))

    def show_text_preview(self, text_data):
        """Show text preview window"""
        try:
            self.is_processing = False
            self.convert_button.config(state='normal', text='Process PDF & Preview Text')
            self.progress.stop()
            self.status_label.config(text="Text processed successfully. Review and confirm conversion.")

            # Show preview window
            preview_window = TextPreviewWindow(
                parent=self.root,
                text_data=text_data,
                config=self.converter.config,
                callback=self.start_audio_conversion
            )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show preview: {e}")

    def start_audio_conversion(self, final_text_data):
        """Start audio conversion with confirmed text"""
        try:
            # Validate voice selection
            if not self.voice_id.get():
                messagebox.showerror("Error", "Please select a voice")
                return

            # Extract voice ID from combo selection
            voice_selection = self.voice_id.get()
            actual_voice_id = voice_selection.split(' - ')[0] if ' - ' in voice_selection else voice_selection

            # Update converter config
            self.converter.config['output']['create_combined'] = self.create_combined.get()
            self.converter.config['output']['create_chapters'] = self.create_chapters.get()
            self.converter.config['output']['create_index'] = True  # Always create index
            self.converter.config['output']['optimize_streaming'] = self.optimize_streaming.get()

            # Store the final text data
            self.extracted_text_data = final_text_data

            # Start conversion in thread
            self.is_converting = True
            self.convert_button.config(state='disabled', text='Converting to Audio...')
            self.progress.start()
            self.status_label.config(text="Converting text to audio...")

            conversion_thread = threading.Thread(
                target=self.run_audio_conversion,
                args=(self.output_dir.get(), actual_voice_id)
            )
            conversion_thread.daemon = True
            conversion_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start audio conversion: {e}")

    def run_audio_conversion(self, output_dir, voice_id):
        """Run the audio conversion process"""
        try:
            # Setup output directory
            pdf_path = Path(self.pdf_path.get())
            if not output_dir:
                output_dir = Path("output") / pdf_path.stem
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            # Initialize TTS
            self.queue.put(('log', "Initializing text-to-speech..."))
            self.converter.initialize_tts()
            self.converter.tts.set_voice(voice_id)

            # Convert to audio using the confirmed text data
            if self.converter.config['text']['detect_chapters'] and len(self.extracted_text_data['chapters']) > 1:
                # Convert chapters separately
                self.queue.put(('log', "Converting chapters to audio..."))
                converted_chapters = self.converter.tts.convert_chapters(
                    chapters=self.extracted_text_data['chapters'],
                    output_dir=output_dir / "chapters",
                    model=self.converter.config['elevenlabs']['model']
                )
            else:
                # Convert as single text with chunks
                self.queue.put(('log', "Converting text to audio chunks..."))
                text_chunks = self.converter.text_cleaner.split_into_chunks(
                    text=self.extracted_text_data['text'],
                    max_length=self.converter.config['text']['max_chunk_length']
                )

                audio_files = self.converter.tts.convert_chunks(
                    text_chunks=text_chunks,
                    output_dir=output_dir / "chunks",
                    base_filename="audiobook_chunk",
                    model=self.converter.config['elevenlabs']['model']
                )

                converted_chapters = [{'audio_file': f, 'title': f'Chunk {i+1}'}
                                    for i, f in enumerate(audio_files)]

            # Process audio files
            self.queue.put(('log', "Processing and combining audio files..."))
            result = self.converter.audio_processor.process_audiobook(
                chapters=converted_chapters,
                output_dir=output_dir,
                config=self.converter.config
            )

            # Add metadata
            result.update({
                'pdf_file': str(pdf_path),
                'extraction_method': self.extraction_method.get(),
                'voice_id': voice_id,
                'total_chapters': len(converted_chapters),
                'output_directory': str(output_dir)
            })

            self.queue.put(('success', result))

        except Exception as e:
            self.queue.put(('error', str(e)))
    

    
    def process_queue(self):
        """Process messages from the conversion thread"""
        try:
            while True:
                message_type, data = self.queue.get_nowait()
                
                if message_type == 'log':
                    self.log_text.insert(tk.END, data + '\n')
                    self.log_text.see(tk.END)

                elif message_type == 'text_ready':
                    self.show_text_preview(data)

                elif message_type == 'success':
                    self.conversion_complete(data)

                elif message_type == 'error':
                    self.conversion_error(data)
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_queue)
    
    def conversion_complete(self, result):
        """Handle successful conversion"""
        self.is_converting = False
        self.is_processing = False
        self.convert_button.config(state='normal', text='Process PDF & Preview Text')
        self.progress.stop()

        self.status_label.config(text="Audiobook conversion completed successfully!")

        # Show success message
        message = f"Audiobook created successfully!\n\n"
        message += f"Chapters: {result['total_chapters']}\n"
        message += f"Duration: {result['total_duration']/3600:.1f} hours\n"
        message += f"Output: {result['output_directory']}"

        messagebox.showinfo("Success", message)

    def conversion_error(self, error_message):
        """Handle conversion error"""
        self.is_converting = False
        self.is_processing = False
        self.convert_button.config(state='normal', text='Process PDF & Preview Text')
        self.progress.stop()

        self.status_label.config(text="Process failed")
        messagebox.showerror("Error", f"Process failed:\n{error_message}")


def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    app = AudiobookConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
