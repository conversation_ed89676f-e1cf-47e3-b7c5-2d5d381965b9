#!/usr/bin/env python3
"""
Test script for Text Preview Window
Author: inkbytefo
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from text_preview_window import TextPreviewWindow

def create_sample_data():
    """Create sample data for testing"""
    return {
        'text': '''Bu bir test kitabıdır. Bu kitap PDF'den sesli kitaba dönüştürme özelliğini test etmek için kullanılmaktadır.

Birinci Bölüm: Giriş

Bu bölümde projenin amacını ve hedeflerini açıklayacağız. PDF dosyalarından metin çıkarma işlemi oldukça karmaşık bir süreçtir ve farklı yöntemler kullanılabilir.

İkinci Bölüm: Teknik Detaylar

Bu bölümde teknik detayları inceleyeceğiz. ElevenLabs API'si kullanarak yüksek kaliteli ses üretimi yapılabilir. Metin işleme ve temizleme algoritmaları da oldukça önemlidir.

Üçüncü Bölüm: Sonuç

Son olarak, projenin başarıyla tamamlandığını ve kullanıcıların artık PDF dosyalarını kolayca sesli kitaba dönüştürebileceğini belirtebiliriz.''',
        
        'chapters': [
            {
                'title': 'Birinci Bölüm: Giriş',
                'content': 'Bu bölümde projenin amacını ve hedeflerini açıklayacağız. PDF dosyalarından metin çıkarma işlemi oldukça karmaşık bir süreçtir ve farklı yöntemler kullanılabilir.'
            },
            {
                'title': 'İkinci Bölüm: Teknik Detaylar', 
                'content': 'Bu bölümde teknik detayları inceleyeceğiz. ElevenLabs API\'si kullanarak yüksek kaliteli ses üretimi yapılabilir. Metin işleme ve temizleme algoritmaları da oldukça önemlidir.'
            },
            {
                'title': 'Üçüncü Bölüm: Sonuç',
                'content': 'Son olarak, projenin başarıyla tamamlandığını ve kullanıcıların artık PDF dosyalarını kolayca sesli kitaba dönüştürebileceğini belirtebiliriz.'
            }
        ],
        
        'metadata': {
            'title': 'Test Kitabı',
            'author': 'inkbytefo',
            'pages': 10,
            'extraction_method': 'pdfplumber'
        }
    }

def test_callback(final_data):
    """Test callback function"""
    print("=== CONVERSION CONFIRMED ===")
    print(f"Final text length: {len(final_data['text'])} characters")
    print(f"Number of chapters: {len(final_data['chapters'])}")
    print(f"Metadata: {final_data['metadata']}")
    
    print("\n=== CHAPTER DETAILS ===")
    for i, chapter in enumerate(final_data['chapters']):
        print(f"Chapter {i+1}: {chapter['title']}")
        print(f"Content length: {len(chapter['content'])} characters")
        print(f"Content preview: {chapter['content'][:100]}...")
        print("-" * 50)

def main():
    """Main test function"""
    print("Starting Text Preview Window Test...")
    
    # Create root window
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    # Create sample data
    sample_data = create_sample_data()
    sample_config = {
        'elevenlabs': {
            'api_key': 'test_key',
            'voice_id': 'test_voice',
            'model': 'eleven_multilingual_v2'
        },
        'text_enhancement': {
            'enabled': True,
            'enhancement_type': 'comprehensive'
        }
    }
    
    print("Sample data created:")
    print(f"- Text length: {len(sample_data['text'])} characters")
    print(f"- Number of chapters: {len(sample_data['chapters'])}")
    print(f"- Metadata: {sample_data['metadata']}")
    
    # Create and show preview window
    try:
        preview_window = TextPreviewWindow(
            parent=root,
            text_data=sample_data,
            config=sample_config,
            callback=test_callback
        )
        
        print("Text Preview Window opened successfully!")
        print("Test the following features:")
        print("1. Chapter navigation")
        print("2. Text editing")
        print("3. Find & Replace")
        print("4. Statistics calculation")
        print("5. Save/Load functionality")
        print("6. Chapter selection")
        print("7. View mode toggle")
        
        # Start main loop
        root.mainloop()
        
    except Exception as e:
        print(f"Error opening preview window: {e}")
        import traceback
        traceback.print_exc()

def test_statistics_calculation():
    """Test statistics calculation separately"""
    print("\n=== TESTING STATISTICS CALCULATION ===")
    
    # Create a temporary preview window for testing
    root = tk.Tk()
    root.withdraw()
    
    sample_data = create_sample_data()
    
    try:
        preview = TextPreviewWindow(root, sample_data, {}, None)
        
        # Test different text lengths
        test_texts = [
            "Short text for testing.",
            "This is a medium length text that should give us some reasonable statistics for testing the calculation functions.",
            "This is a very long text that we will use to test the statistics calculation function. " * 100
        ]
        
        for i, text in enumerate(test_texts):
            print(f"\nTest {i+1}:")
            print(f"Text length: {len(text)} characters")
            print(f"Word count: {len(text.split())} words")
            
            # Calculate cost
            cost = preview.calculate_elevenlabs_cost(len(text))
            print(f"Estimated cost: ${cost:.2f}")
            
        root.destroy()
        print("Statistics calculation test completed successfully!")
        
    except Exception as e:
        print(f"Error in statistics test: {e}")
        root.destroy()

def test_find_replace():
    """Test find and replace functionality"""
    print("\n=== TESTING FIND & REPLACE ===")
    print("This test requires manual interaction with the GUI")
    print("1. Open the preview window")
    print("2. Use Ctrl+F or the Find & Replace button")
    print("3. Test case sensitive and whole word options")
    print("4. Test replace and replace all functions")

if __name__ == "__main__":
    print("PDF to Audiobook - Text Preview Window Test")
    print("=" * 50)
    
    # Run tests
    test_statistics_calculation()
    test_find_replace()
    
    print("\nStarting interactive test...")
    main()
